import React, { useCallback } from "react";
import { Button, Result, ResultProps } from "antd";
import { useNavigate } from "react-router-dom";

interface ErrorPageProps {
  status: ResultProps["status"];
  title?: string;
  subTitle?: string;
}

const CommonErrorPage: React.FC<ErrorPageProps> = ({
  status,
  title,
  subTitle,
}) => {
  const navigate = useNavigate();

  const handleBackHome = useCallback(() => {
    navigate("/products");
  }, [navigate]);

  const defaultTitles: Record<
    Exclude<ResultProps["status"], undefined>,
    string
  > = {
    "404": "Page Not Found",
    "403": "Forbidden",
    "500": "Internal Server Error",
    error: "Error",
    success: "Success",
    info: "Information",
    warning: "Warning",
  };

  const defaultSubTitles: Record<
    Exclude<ResultProps["status"], undefined>,
    string
  > = {
    "404": "Sorry, the page you visited does not exist.",
    "403": "Sorry, you are not authorized to access this page.",
    "500": "Something went wrong. Please try again later.",
    error: "An error occurred. Please try again later.",
    success: "Operation completed successfully.",
    info: "Here is some information.",
    warning: "Please be cautious.",
  };

  // Provide a fallback in case `status` is undefined
  const resolvedStatus = status || "error";

  return (
    <div className="error-page-container">
      <Result
        status={resolvedStatus}
        title={title || defaultTitles[resolvedStatus]}
        subTitle={subTitle || defaultSubTitles[resolvedStatus]}
        extra={
          <Button
            className="btn-back-home"
            type="primary"
            onClick={handleBackHome}
          >
            Back Home
          </Button>
        }
      />
    </div>
  );
};

export default CommonErrorPage;
